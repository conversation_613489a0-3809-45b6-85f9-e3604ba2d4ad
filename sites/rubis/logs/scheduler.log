2025-08-20 10:01:44,087 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-20 10:01:44,092 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-20 10:01:44,094 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-20 10:01:44,096 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for rubis
2025-08-20 10:01:44,098 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:01:44,100 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for rubis
2025-08-20 10:01:44,102 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-20 10:01:44,105 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for rubis
2025-08-20 10:01:44,107 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-20 10:01:44,109 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-20 10:01:44,111 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-20 10:01:44,113 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-20 10:01:44,115 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for rubis
2025-08-20 10:01:44,117 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-20 10:01:44,122 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for rubis
2025-08-20 10:01:44,124 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for rubis
2025-08-20 10:01:44,127 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-20 10:01:44,129 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-20 10:01:44,132 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-20 10:01:44,135 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for rubis
2025-08-20 10:01:44,137 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for rubis
2025-08-20 10:01:44,139 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for rubis
2025-08-20 10:01:44,141 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for rubis
2025-08-20 10:01:44,143 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for rubis
2025-08-20 10:02:44,554 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-20 10:02:44,557 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-20 10:02:44,568 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-20 10:02:44,609 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-20 10:02:44,677 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-20 10:02:44,683 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-20 10:02:44,717 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-20 10:02:44,758 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-20 10:02:44,790 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-20 10:02:44,840 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-20 10:02:44,857 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-20 10:02:44,891 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:02:44,895 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-20 10:02:44,917 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-20 10:02:44,926 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-20 10:02:44,931 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-20 10:02:44,951 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-20 10:02:44,955 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-20 10:02:44,966 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-20 10:02:45,004 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:02:45,013 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-20 10:02:45,029 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-20 10:02:45,038 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-20 10:02:45,046 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-20 10:02:45,048 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-20 10:02:45,054 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-20 10:02:45,059 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-20 10:02:45,063 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-20 10:02:45,065 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-20 10:02:45,088 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-20 10:02:45,102 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-20 10:02:45,116 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-20 10:02:45,130 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-20 10:03:45,177 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-20 10:03:45,181 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:03:45,184 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-20 10:03:45,186 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-20 10:03:45,188 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-20 10:03:45,190 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-20 10:03:45,196 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:03:45,202 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-20 10:03:45,208 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-20 10:03:45,211 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-20 10:03:45,212 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-20 10:03:45,221 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-20 10:03:45,223 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-20 10:03:45,227 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-20 10:03:45,231 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-20 10:03:45,233 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-20 10:03:45,234 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-20 10:03:45,237 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-20 10:03:45,244 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-20 10:03:45,246 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-20 10:03:45,249 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-20 10:03:45,258 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-20 10:03:45,267 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-20 10:03:45,270 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-20 10:03:45,274 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-20 10:03:45,277 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-20 10:03:45,282 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-20 10:03:45,290 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-20 10:03:45,293 ERROR scheduler Skipped queueing wiki.wiki.doctype.wiki_page.search.build_index_in_background because it was found in queue for rubis
2025-08-20 10:03:45,295 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-20 10:03:45,298 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-20 10:03:45,300 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-20 10:03:45,302 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-20 10:04:45,713 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-20 10:04:45,716 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-20 10:04:45,727 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-20 10:04:45,730 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:04:45,734 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-20 10:04:45,737 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-20 10:04:45,738 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-20 10:04:45,749 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:04:45,751 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-20 10:04:45,752 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-20 10:04:45,754 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-20 10:04:45,757 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-20 10:04:45,760 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-20 10:04:45,762 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-20 10:04:45,769 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-20 10:04:45,771 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-20 10:04:45,780 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-20 10:04:45,783 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-20 10:04:45,784 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-20 10:04:45,786 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-20 10:04:45,800 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-20 10:04:45,805 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-20 10:04:45,808 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-20 10:04:45,813 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-20 10:04:45,814 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-20 10:04:45,820 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-20 10:04:45,826 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-20 10:04:45,829 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-20 10:04:45,834 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-20 10:04:45,837 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-20 10:04:45,840 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-20 10:04:45,842 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-20 10:05:45,864 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-20 10:05:45,866 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
2025-08-20 10:05:45,875 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for rubis
2025-08-20 10:05:45,876 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-20 10:05:45,877 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-20 10:05:45,879 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-20 10:05:45,883 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-20 10:05:45,885 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-20 10:05:45,887 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:05:45,899 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for rubis
2025-08-20 10:05:45,904 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-20 10:05:45,907 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-20 10:05:45,910 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-20 10:05:45,915 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-20 10:05:45,919 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-20 10:05:45,923 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-20 10:05:45,927 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-20 10:05:45,933 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:05:45,940 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-20 10:05:45,942 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-20 10:05:45,947 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-20 10:05:45,949 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for rubis
2025-08-20 10:05:45,951 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-20 10:05:45,953 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-20 10:05:45,960 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-20 10:05:45,966 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-20 10:05:45,971 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-20 10:05:45,973 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-20 10:05:45,975 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-20 10:05:45,976 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for rubis
2025-08-20 10:05:45,981 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-20 10:05:45,983 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for rubis
2025-08-20 10:05:45,985 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-20 10:05:45,987 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-20 10:05:45,992 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for rubis
2025-08-20 10:05:46,002 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-20 10:05:46,006 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-20 10:05:46,008 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-20 10:06:46,414 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for rubis
2025-08-20 10:06:46,418 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for rubis
2025-08-20 10:06:46,420 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for rubis
2025-08-20 10:06:46,423 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for rubis
2025-08-20 10:06:46,426 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for rubis
2025-08-20 10:06:46,433 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for rubis
2025-08-20 10:06:46,437 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for rubis
2025-08-20 10:06:46,444 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for rubis
2025-08-20 10:06:46,450 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for rubis
2025-08-20 10:06:46,452 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for rubis
2025-08-20 10:06:46,454 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:06:46,459 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for rubis
2025-08-20 10:06:46,460 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for rubis
2025-08-20 10:06:46,462 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for rubis
2025-08-20 10:06:46,470 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for rubis
2025-08-20 10:06:46,481 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for rubis
2025-08-20 10:06:46,483 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for rubis
2025-08-20 10:06:46,493 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for rubis
2025-08-20 10:06:46,504 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for rubis
2025-08-20 10:06:46,506 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for rubis
2025-08-20 10:06:46,508 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for rubis
2025-08-20 10:06:46,512 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for rubis
2025-08-20 10:06:46,515 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for rubis
2025-08-20 10:06:46,522 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for rubis
2025-08-20 10:06:46,525 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for rubis
2025-08-20 10:06:46,527 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for rubis
2025-08-20 10:06:46,537 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for rubis
2025-08-20 10:06:46,539 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for rubis
2025-08-20 10:06:46,546 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for rubis
2025-08-20 10:06:46,549 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for rubis
2025-08-20 10:06:46,556 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for rubis
2025-08-20 10:06:46,559 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for rubis
